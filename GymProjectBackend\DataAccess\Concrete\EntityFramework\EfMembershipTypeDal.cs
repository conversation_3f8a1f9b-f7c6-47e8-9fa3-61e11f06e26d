using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipTypeDal : EfCompanyEntityRepositoryBase<MembershipType, GymContext>, IMembershiptypeDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMembershipTypeDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfMembershipTypeDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<PackageWithCountDto> GetPackagesByBranch(string branch)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                var packagesWithCount = from mt in _context.MembershipTypes
                                       where mt.Branch == branch
                                       && mt.IsActive == true
                                       && mt.CompanyID == companyId
                                       select new PackageWithCountDto
                                       {
                                           MembershipTypeID = mt.MembershipTypeID,
                                           Branch = mt.Branch,
                                           TypeName = mt.TypeName,
                                           Day = mt.Day,
                                           Price = mt.Price,
                                           MemberCount = _context.Memberships.Count(m =>
                                               m.MembershipTypeID == mt.MembershipTypeID
                                               && m.IsActive == true
                                               && m.EndDate > now
                                               && m.CompanyID == companyId)
                                       };

                // Sadece en az 1 üyesi olan paketleri döndür
                return packagesWithCount.Where(p => p.MemberCount > 0).ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public PaginatedResult<MembershipType> GetAllPaginated(MembershipTypePagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Base query - sadece aktif üyelik türleri
                var query = _context.MembershipTypes
                    .Where(mt => mt.IsActive == true && mt.CompanyID == companyId);

                // Arama filtresi
                if (!string.IsNullOrEmpty(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    query = query.Where(mt =>
                        mt.Branch.ToLower().Contains(searchText) ||
                        mt.TypeName.ToLower().Contains(searchText));
                }

                // Branş filtresi
                if (!string.IsNullOrEmpty(parameters.Branch))
                {
                    query = query.Where(mt => mt.Branch == parameters.Branch);
                }

                // Fiyat aralığı filtresi
                if (parameters.MinPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price >= parameters.MinPrice.Value);
                }
                if (parameters.MaxPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price <= parameters.MaxPrice.Value);
                }

                // Süre aralığı filtresi
                if (parameters.MinDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day >= parameters.MinDuration.Value);
                }
                if (parameters.MaxDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day <= parameters.MaxDuration.Value);
                }

                // Sıralama - En son eklenen en üstte (ID'ye göre azalan)
                var orderedQuery = query.OrderByDescending(mt => mt.MembershipTypeID);

                // Toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MembershipType>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<BranchGetAllDto> GetBranchesAndTypes()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();

                var result = _context.MembershipTypes
                    .Where(mt => mt.IsActive == true && mt.CompanyID == companyId)
                    .Select(mt => new BranchGetAllDto
                    {
                        MembershipTypeID = mt.MembershipTypeID,
                        Branch = mt.Branch,
                        TypeName = mt.TypeName
                    })
                    .ToList();

                return result;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        // SOLID prensiplerine uygun: Complex business operations DAL'da
        public IResult SoftDeleteMembershipType(int membershipTypeId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var membershipType = _context.MembershipTypes.FirstOrDefault(mt =>
                        mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId);

                    if (membershipType == null)
                    {
                        return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Soft delete işlemi
                    membershipType.IsActive = false;
                    membershipType.DeletedDate = DateTime.Now;
                    _context.SaveChanges();

                    return new SuccessResult("Üyelik türü başarıyla silindi.");
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik türü silinirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateMembershipTypeWithBusinessLogic(MembershipType membershipType, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var existingMembershipType = _context.MembershipTypes.FirstOrDefault(mt =>
                        mt.MembershipTypeID == membershipType.MembershipTypeID && mt.CompanyID == companyId);

                    if (existingMembershipType == null)
                    {
                        return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Mevcut entity'nin özelliklerini güncelle (Entity tracking sorununu önler)
                    existingMembershipType.TypeName = membershipType.TypeName;
                    existingMembershipType.Day = membershipType.Day;
                    existingMembershipType.Price = membershipType.Price;
                    existingMembershipType.Branch = membershipType.Branch;
                    existingMembershipType.IsActive = membershipType.IsActive;
                    existingMembershipType.UpdatedDate = DateTime.Now;
                    // CreationDate ve CompanyID değişmez

                    _context.SaveChanges();

                    return new SuccessResult("Üyelik türü başarıyla güncellendi.");
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik türü güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
