﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyAdressDal : EfEntityRepositoryBase<CompanyAdress, GymContext>, ICompanyAdressDal
    {
        // Constructor injection (Scalability için)
        public EfCompanyAdressDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfCompanyAdressDal() : base()
        {
        }

        public List<CompanyAdressDetailDto> GetCompanyAdressDetails()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from ca in _context.CompanyAdresses
                             join c in _context.Companies on ca.CompanyID equals c.CompanyID
                             join ci in _context.Cities on ca.CityID equals ci.CityID
                             join t in _context.Towns on ca.TownID equals t.TownID
                             where ca.IsActive==true
                             select new CompanyAdressDetailDto
                             {
                                 CompanyAdressID = ca.CompanyAdressID,
                                 CompanyName = c.CompanyName,
                                 CityName = ci.CityName,
                                 TownName = t.TownName,
                                 Adress = ca.Adress,
                                 isActive = ca.IsActive
                             };
                return result.ToList();
            }
        }
    } 
}
