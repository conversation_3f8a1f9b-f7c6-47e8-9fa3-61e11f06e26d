using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyExerciseDal : EfCompanyEntityRepositoryBase<CompanyExercise, GymContext>, ICompanyExerciseDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfCompanyExerciseDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfCompanyExerciseDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<CompanyExerciseDto> GetCompanyExercises(int companyId)
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ce in _context.CompanyExercises
                         join ec in _context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                         orderby ec.CategoryName, ce.ExerciseName
                         select new CompanyExerciseDto
                         {
                             CompanyExerciseID = ce.CompanyExerciseID,
                             CompanyID = ce.CompanyID,
                             ExerciseCategoryID = ce.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             ExerciseName = ce.ExerciseName,
                             Description = ce.Description,
                             Instructions = ce.Instructions,
                             MuscleGroups = ce.MuscleGroups,
                             Equipment = ce.Equipment,
                             DifficultyLevel = ce.DifficultyLevel,
                             DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                  ce.DifficultyLevel == 2 ? "Orta" :
                                                  ce.DifficultyLevel == 3 ? "İleri" : "",
                             IsActive = ce.IsActive,
                             CreationDate = ce.CreationDate
                         };
            return result.ToList();
        }

        public List<CompanyExerciseDto> GetCompanyExercisesByCategory(int companyId, int categoryId)
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ce in _context.CompanyExercises
                         join ec in _context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where ce.CompanyID == companyId && ce.ExerciseCategoryID == categoryId &&
                               ce.IsActive == true && ec.IsActive == true
                         orderby ce.ExerciseName
                         select new CompanyExerciseDto
                         {
                             CompanyExerciseID = ce.CompanyExerciseID,
                             CompanyID = ce.CompanyID,
                             ExerciseCategoryID = ce.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             ExerciseName = ce.ExerciseName,
                             Description = ce.Description,
                             Instructions = ce.Instructions,
                             MuscleGroups = ce.MuscleGroups,
                             Equipment = ce.Equipment,
                             DifficultyLevel = ce.DifficultyLevel,
                             DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                  ce.DifficultyLevel == 2 ? "Orta" :
                                                  ce.DifficultyLevel == 3 ? "İleri" : "",
                             IsActive = ce.IsActive,
                             CreationDate = ce.CreationDate
                         };
            return result.ToList();
        }

        public PaginatedResult<CompanyExerciseDto> GetCompanyExercisesFiltered(int companyId, CompanyExerciseFilterDto filter)
        {
            using (GymContext context = new GymContext())
            {
                var query = from ce in context.CompanyExercises
                           join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                           where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                           select new CompanyExerciseDto
                           {
                               CompanyExerciseID = ce.CompanyExerciseID,
                               CompanyID = ce.CompanyID,
                               ExerciseCategoryID = ce.ExerciseCategoryID,
                               CategoryName = ec.CategoryName,
                               ExerciseName = ce.ExerciseName,
                               Description = ce.Description,
                               Instructions = ce.Instructions,
                               MuscleGroups = ce.MuscleGroups,
                               Equipment = ce.Equipment,
                               DifficultyLevel = ce.DifficultyLevel,
                               DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" : 
                                                    ce.DifficultyLevel == 2 ? "Orta" : 
                                                    ce.DifficultyLevel == 3 ? "İleri" : "",
                               IsActive = ce.IsActive,
                               CreationDate = ce.CreationDate
                           };

                // Filtreleme
                if (filter.ExerciseCategoryID.HasValue)
                {
                    query = query.Where(x => x.ExerciseCategoryID == filter.ExerciseCategoryID.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                {
                    var searchTerm = filter.SearchTerm.ToLower();
                    query = query.Where(x =>
                        (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                        (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                        (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
                }

                if (filter.DifficultyLevel.HasValue)
                {
                    query = query.Where(x => x.DifficultyLevel == filter.DifficultyLevel.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.Equipment))
                {
                    var equipment = filter.Equipment.ToLower();
                    query = query.Where(x => x.Equipment.ToLower().Contains(equipment));
                }

                // Sıralama
                query = query.OrderBy(x => x.CategoryName).ThenBy(x => x.ExerciseName);

                return query.ToPaginatedResult(filter.Page, filter.PageSize);
            }
        }

        public List<CompanyExerciseDto> SearchCompanyExercises(int companyId, string searchTerm)
        {
            using (GymContext context = new GymContext())
            {
                // Null kontrolü ekle
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return new List<CompanyExerciseDto>();
                }

                var result = from ce in context.CompanyExercises
                             join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true &&
                                   (ce.ExerciseName.Contains(searchTerm) ||
                                    (ce.Description != null && ce.Description.Contains(searchTerm)) ||
                                    (ce.MuscleGroups != null && ce.MuscleGroups.Contains(searchTerm)) ||
                                    ec.CategoryName.Contains(searchTerm))
                             orderby ce.ExerciseName
                             select new CompanyExerciseDto
                             {
                                 CompanyExerciseID = ce.CompanyExerciseID,
                                 CompanyID = ce.CompanyID,
                                 ExerciseCategoryID = ce.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = ce.ExerciseName,
                                 Description = ce.Description,
                                 Instructions = ce.Instructions,
                                 MuscleGroups = ce.MuscleGroups,
                                 Equipment = ce.Equipment,
                                 DifficultyLevel = ce.DifficultyLevel,
                                 DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                      ce.DifficultyLevel == 2 ? "Orta" :
                                                      ce.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = ce.IsActive,
                                 CreationDate = ce.CreationDate
                             };
                return result.ToList();
            }
        }

        public CompanyExerciseDto GetCompanyExerciseDetail(int companyId, int exerciseId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from ce in context.CompanyExercises
                             join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where ce.CompanyID == companyId && ce.CompanyExerciseID == exerciseId && ce.IsActive == true
                             select new CompanyExerciseDto
                             {
                                 CompanyExerciseID = ce.CompanyExerciseID,
                                 CompanyID = ce.CompanyID,
                                 ExerciseCategoryID = ce.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = ce.ExerciseName,
                                 Description = ce.Description,
                                 Instructions = ce.Instructions,
                                 MuscleGroups = ce.MuscleGroups,
                                 Equipment = ce.Equipment,
                                 DifficultyLevel = ce.DifficultyLevel,
                                 DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" : 
                                                      ce.DifficultyLevel == 2 ? "Orta" : 
                                                      ce.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = ce.IsActive,
                                 CreationDate = ce.CreationDate
                             };
                return result.FirstOrDefault();
            }
        }

        // Birleşik egzersiz listesi (System + Company)
        public List<CombinedExerciseDto> GetCombinedExercises(int companyId)
        {
            using (GymContext context = new GymContext())
            {
                var systemExercises = from se in context.SystemExercises
                                     join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                     where se.IsActive == true && ec.IsActive == true
                                     select new CombinedExerciseDto
                                     {
                                         ExerciseID = se.SystemExerciseID,
                                         ExerciseType = "System",
                                         ExerciseCategoryID = se.ExerciseCategoryID,
                                         CategoryName = ec.CategoryName,
                                         ExerciseName = se.ExerciseName,
                                         Description = se.Description,
                                         Instructions = se.Instructions,
                                         MuscleGroups = se.MuscleGroups,
                                         Equipment = se.Equipment,
                                         DifficultyLevel = se.DifficultyLevel,
                                         DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                              se.DifficultyLevel == 2 ? "Orta" : 
                                                              se.DifficultyLevel == 3 ? "İleri" : "",
                                         IsActive = se.IsActive,
                                         CreationDate = se.CreationDate
                                     };

                var companyExercises = from ce in context.CompanyExercises
                                      join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                      where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                                      select new CombinedExerciseDto
                                      {
                                          ExerciseID = ce.CompanyExerciseID,
                                          ExerciseType = "Company",
                                          ExerciseCategoryID = ce.ExerciseCategoryID,
                                          CategoryName = ec.CategoryName,
                                          ExerciseName = ce.ExerciseName,
                                          Description = ce.Description,
                                          Instructions = ce.Instructions,
                                          MuscleGroups = ce.MuscleGroups,
                                          Equipment = ce.Equipment,
                                          DifficultyLevel = ce.DifficultyLevel,
                                          DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" : 
                                                               ce.DifficultyLevel == 2 ? "Orta" : 
                                                               ce.DifficultyLevel == 3 ? "İleri" : "",
                                          IsActive = ce.IsActive,
                                          CreationDate = ce.CreationDate
                                      };

                var combined = systemExercises.ToList().Concat(companyExercises.ToList())
                              .OrderBy(x => x.CategoryName)
                              .ThenBy(x => x.ExerciseType) // Önce System, sonra Company
                              .ThenBy(x => x.ExerciseName)
                              .ToList();

                return combined;
            }
        }

        public List<CombinedExerciseDto> GetCombinedExercisesByCategory(int companyId, int categoryId)
        {
            using (GymContext context = new GymContext())
            {
                var systemExercises = from se in context.SystemExercises
                                     join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                     where se.ExerciseCategoryID == categoryId && se.IsActive == true && ec.IsActive == true
                                     select new CombinedExerciseDto
                                     {
                                         ExerciseID = se.SystemExerciseID,
                                         ExerciseType = "System",
                                         ExerciseCategoryID = se.ExerciseCategoryID,
                                         CategoryName = ec.CategoryName,
                                         ExerciseName = se.ExerciseName,
                                         Description = se.Description,
                                         Instructions = se.Instructions,
                                         MuscleGroups = se.MuscleGroups,
                                         Equipment = se.Equipment,
                                         DifficultyLevel = se.DifficultyLevel,
                                         DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                              se.DifficultyLevel == 2 ? "Orta" : 
                                                              se.DifficultyLevel == 3 ? "İleri" : "",
                                         IsActive = se.IsActive,
                                         CreationDate = se.CreationDate
                                     };

                var companyExercises = from ce in context.CompanyExercises
                                      join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                      where ce.CompanyID == companyId && ce.ExerciseCategoryID == categoryId && 
                                            ce.IsActive == true && ec.IsActive == true
                                      select new CombinedExerciseDto
                                      {
                                          ExerciseID = ce.CompanyExerciseID,
                                          ExerciseType = "Company",
                                          ExerciseCategoryID = ce.ExerciseCategoryID,
                                          CategoryName = ec.CategoryName,
                                          ExerciseName = ce.ExerciseName,
                                          Description = ce.Description,
                                          Instructions = ce.Instructions,
                                          MuscleGroups = ce.MuscleGroups,
                                          Equipment = ce.Equipment,
                                          DifficultyLevel = ce.DifficultyLevel,
                                          DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" : 
                                                               ce.DifficultyLevel == 2 ? "Orta" : 
                                                               ce.DifficultyLevel == 3 ? "İleri" : "",
                                          IsActive = ce.IsActive,
                                          CreationDate = ce.CreationDate
                                      };

                var combined = systemExercises.ToList().Concat(companyExercises.ToList())
                              .OrderBy(x => x.ExerciseType) // Önce System, sonra Company
                              .ThenBy(x => x.ExerciseName)
                              .ToList();

                return combined;
            }
        }

        public PaginatedResult<CombinedExerciseDto> GetCombinedExercisesFiltered(int companyId, SystemExerciseFilterDto filter)
        {
            var combinedExercises = GetCombinedExercises(companyId).AsQueryable();

            // Filtreleme
            if (filter.ExerciseCategoryID.HasValue)
            {
                combinedExercises = combinedExercises.Where(x => x.ExerciseCategoryID == filter.ExerciseCategoryID.Value);
            }

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                combinedExercises = combinedExercises.Where(x =>
                    (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                    (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
            }

            if (filter.DifficultyLevel.HasValue)
            {
                combinedExercises = combinedExercises.Where(x => x.DifficultyLevel == filter.DifficultyLevel.Value);
            }

            if (!string.IsNullOrWhiteSpace(filter.Equipment))
            {
                var equipment = filter.Equipment.ToLower();
                combinedExercises = combinedExercises.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
            }

            // Egzersiz türü filtresi
            if (!string.IsNullOrWhiteSpace(filter.ExerciseType))
            {
                combinedExercises = combinedExercises.Where(x => x.ExerciseType == filter.ExerciseType);
            }

            // Sıralama
            combinedExercises = combinedExercises.OrderBy(x => x.CategoryName)
                                                .ThenBy(x => x.ExerciseType)
                                                .ThenBy(x => x.ExerciseName);

            return combinedExercises.ToPaginatedResult(filter.Page, filter.PageSize);
        }

        // SOLID prensiplerine uygun refactoring için eklenen metot
        public IResult UpdateCompanyExercise(int companyId, CompanyExerciseUpdateDto exerciseUpdateDto)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var existingExercise = _context.CompanyExercises
                        .FirstOrDefault(e => e.CompanyExerciseID == exerciseUpdateDto.CompanyExerciseID &&
                                           e.CompanyID == companyId);

                    if (existingExercise == null)
                    {
                        return new ErrorResult("Salon egzersizi bulunamadı.");
                    }

                    // Entity property'lerini güncelle
                    existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                    existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                    existingExercise.Description = exerciseUpdateDto.Description;
                    existingExercise.Instructions = exerciseUpdateDto.Instructions;
                    existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                    existingExercise.Equipment = exerciseUpdateDto.Equipment;
                    existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                    existingExercise.IsActive = exerciseUpdateDto.IsActive;
                    existingExercise.UpdatedDate = DateTime.Now;

                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var existingExercise = context.CompanyExercises
                            .FirstOrDefault(e => e.CompanyExerciseID == exerciseUpdateDto.CompanyExerciseID &&
                                               e.CompanyID == companyId);

                        if (existingExercise == null)
                        {
                            return new ErrorResult("Salon egzersizi bulunamadı.");
                        }

                        // Entity property'lerini güncelle
                        existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                        existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                        existingExercise.Description = exerciseUpdateDto.Description;
                        existingExercise.Instructions = exerciseUpdateDto.Instructions;
                        existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                        existingExercise.Equipment = exerciseUpdateDto.Equipment;
                        existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                        existingExercise.IsActive = exerciseUpdateDto.IsActive;
                        existingExercise.UpdatedDate = DateTime.Now;

                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Salon egzersizi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult AddCompanyExercise(CompanyExerciseAddDto exerciseAddDto, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var exercise = new CompanyExercise
                    {
                        CompanyID = companyId,
                        ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                        ExerciseName = exerciseAddDto.ExerciseName,
                        Description = exerciseAddDto.Description,
                        Instructions = exerciseAddDto.Instructions,
                        MuscleGroups = exerciseAddDto.MuscleGroups,
                        Equipment = exerciseAddDto.Equipment,
                        DifficultyLevel = exerciseAddDto.DifficultyLevel,
                        IsActive = true,
                        CreationDate = DateTime.Now
                    };

                    _context.CompanyExercises.Add(exercise);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        var exercise = new CompanyExercise
                        {
                            CompanyID = companyId,
                            ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                            ExerciseName = exerciseAddDto.ExerciseName,
                            Description = exerciseAddDto.Description,
                            Instructions = exerciseAddDto.Instructions,
                            MuscleGroups = exerciseAddDto.MuscleGroups,
                            Equipment = exerciseAddDto.Equipment,
                            DifficultyLevel = exerciseAddDto.DifficultyLevel,
                            IsActive = true,
                            CreationDate = DateTime.Now
                        };

                        context.CompanyExercises.Add(exercise);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Salon egzersizi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi eklenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID prensiplerine uygun: Soft delete işlemi DAL katmanında
        public IResult SoftDeleteCompanyExercise(int exerciseId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var exercise = _context.CompanyExercises.FirstOrDefault(e =>
                        e.CompanyExerciseID == exerciseId && e.CompanyID == companyId);

                    if (exercise == null)
                    {
                        return new ErrorResult("Salon egzersizi bulunamadı.");
                    }

                    // Soft delete işlemi
                    exercise.IsActive = false;
                    exercise.DeletedDate = DateTime.Now;
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        var exercise = context.CompanyExercises.FirstOrDefault(e =>
                            e.CompanyExerciseID == exerciseId && e.CompanyID == companyId);

                        if (exercise == null)
                        {
                            return new ErrorResult("Salon egzersizi bulunamadı.");
                        }

                        // Soft delete işlemi
                        exercise.IsActive = false;
                        exercise.DeletedDate = DateTime.Now;
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Salon egzersizi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi silinirken hata oluştu: {ex.Message}");
            }
        }
    }
}
