using Business.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TransactionsController : ControllerBase
    {
        private ITransactionService _transactionService;

        public TransactionsController(ITransactionService transactionService)
        {
            _transactionService = transactionService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _transactionService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("updateallpaymentstatus/{memberId}")]
        public IActionResult UpdateAllPaymentStatus(int memberId)
        {
            var result = _transactionService.UpdateAllPaymentStatus(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getbymemberid")]
        public IActionResult GetByMemberId(int memberId)
        {
            var result = _transactionService.GetByMemberId(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(Transaction transaction)
        {
            var result = _transactionService.Add(transaction);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getwithuserproductdetails")]
        public IActionResult GetWithUserProductDetails()
        {
            var result = _transactionService.GetTransactionsWithDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("updatepaymentstatus/{id}")]
        public IActionResult UpdatePaymentStatus(int id)
        {
            var result = _transactionService.UpdatePaymentStatus(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("addBulk")]
        public IActionResult AddBulk([FromBody] BulkTransactionDto bulkTransaction)
        {
            var result = _transactionService.AddBulk(bulkTransaction);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getunpaidtransactions")]
        public IActionResult GetUnpaidTransactions(int memberId)
        {
            var result = _transactionService.GetUnpaidTransactions(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpDelete("delete/{id}")]
        public IActionResult Delete(int id)
        {
            var result = _transactionService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getmonthlytotal")]
        public IActionResult GetMonthlyTransactionTotal([FromQuery] int year, [FromQuery] int month)
        {
            if (year < 1900 || year > DateTime.Now.Year + 5 || month < 1 || month > 12)
            {
                return BadRequest("Geçersiz yıl veya ay değeri.");
            }

            var result = _transactionService.GetMonthlyTransactionTotal(year, month);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getdailytotal")]
        public IActionResult GetDailyTransactionTotal([FromQuery] string date)
        {
            if (!DateTime.TryParse(date, out DateTime parsedDate))
            {
                return BadRequest("Geçersiz tarih formatı. YYYY-MM-DD formatında tarih gönderiniz.");
            }

            var result = _transactionService.GetDailyTransactionTotal(parsedDate);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}